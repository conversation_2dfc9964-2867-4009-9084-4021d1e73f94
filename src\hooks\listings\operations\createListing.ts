
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Listing, NewListing } from '../types';
import { useAuth } from '@/contexts/AuthContext';

export const useCreateListing = (
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
  setListings: React.Dispatch<React.SetStateAction<Listing[]>>,
  setFeaturedListings: React.Dispatch<React.SetStateAction<Listing[]>>,
  fetchListings: () => Promise<void>
) => {
  const { user } = useAuth();

  const createListing = async (listingData: Omit<NewListing, 'user_id'>) => {
    if (!user) {
      toast.error("Требуется авторизация", {
        description: "Вы должны войти в систему, чтобы создать объявление"
      });
      throw new Error('User must be authenticated to create a listing');
    }

    try {
      setIsLoading(true);

      const image_urls = listingData.image_urls || [];

      // Проверяем обязательные поля
      if (!listingData.title || !listingData.make || !listingData.model || !listingData.location) {
        throw new Error('Missing required fields');
      }

      // Убедимся, что числовые поля имеют правильный тип
      const price = typeof listingData.price === 'string' ? parseFloat(listingData.price) : listingData.price;
      const year = typeof listingData.year === 'string' ? parseInt(listingData.year) : listingData.year;

      if (isNaN(price) || price <= 0) {
        throw new Error('Invalid price value');
      }

      if (isNaN(year) || year < 1900 || year > new Date().getFullYear()) {
        throw new Error('Invalid year value');
      }

      const newListing = {
        ...listingData,
        price,
        year,
        user_id: user.id,
        image_urls,
        status: listingData.status || 'active',
        // Since we've updated the types, these fields are now properly typed
        body_type: listingData.body_type || null,
        fuel_type: listingData.fuel_type || null,
        transmission: listingData.transmission || null,
        color: listingData.color || null,
        mileage: listingData.mileage !== undefined ? Number(listingData.mileage) : null
      };

      console.log('Creating listing with data:', newListing);

      const { data, error } = await supabase
        .from('listings')
        .insert(newListing)
        .select();

      if (error) {
        console.error('Supabase error creating listing:', error);
        throw error;
      }

      if (!data || data.length === 0) {
        throw new Error('No data returned from the listing creation');
      }

      const createdListing = data[0] as Listing;
      console.log('Listing created successfully:', createdListing);

      // Add to local state right away to avoid waiting for the next fetch
      setListings(prevListings => [createdListing, ...prevListings]);

      if (createdListing.featured) {
        setFeaturedListings(prevFeatured => {
          const newFeatured = [createdListing, ...prevFeatured];
          return newFeatured.slice(0, 4);
        });
      }

      // Also refresh from server to ensure we have the latest data
      await fetchListings();

      // Success message is handled by the frontend form submission
      return createdListing;
    } catch (error: any) {
      console.error('Error creating listing:', error);

      // Более информативные сообщения об ошибках
      if (error.message === 'Missing required fields') {
        toast.error("Отсутствуют обязательные поля", {
          description: "Пожалуйста, заполните все обязательные поля"
        });
      } else if (error.message === 'Invalid price value') {
        toast.error("Неверное значение цены", {
          description: "Цена должна быть положительным числом"
        });
      } else if (error.message === 'Invalid year value') {
        toast.error("Неверный год", {
          description: "Год должен быть между 1900 и текущим годом"
        });
      } else if (error.code === '23505') { // Код ошибки для дубликата в Postgres
        toast.error("Дублирующееся объявление", {
          description: "Похожее объявление уже существует"
        });
      } else {
        toast.error("Ошибка при создании объявления", {
          description: "Пожалуйста, попробуйте еще раз или свяжитесь с поддержкой"
        });
      }

      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { createListing };
};
